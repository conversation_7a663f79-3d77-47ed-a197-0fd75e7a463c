package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.LogKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.dto.CropEnhanceResponse;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档切边矫正服务类
 * 处理文档矫正和角点检测的核心业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public class CropEnhanceService {
    
    public static final CropEnhanceService me = new CropEnhanceService();
    
    // 支持的图片格式
    private static final String[] SUPPORTED_FORMATS = {"jpg", "jpeg", "png", "bmp", "webp"};
    
    // 最大文件大小 (10MB)
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;
    
    /**
     * 文档矫正和角点检测
     *
     * @param imageFile 上传的图片文件
     * @param enhanceMode 增强模式
     * @param cropImage 是否执行切边
     * @param sizeAndPosition 自定义切边区域
     * @return 矫正结果
     */
    /**
     * 文档矫正和角点检测（向后兼容方法）
     *
     * @param imageFile 上传的图片文件
     * @param enhanceMode 增强模式
     * @param cropImage 是否执行切边
     * @return 矫正结果
     */
    public RetKit correctDocumentAndDetectCorners(UploadFile imageFile, Integer enhanceMode, Integer cropImage) {
        return correctDocumentAndDetectCorners(imageFile, enhanceMode, cropImage, null);
    }

    public RetKit correctDocumentAndDetectCorners(UploadFile imageFile, Integer enhanceMode, Integer cropImage, String sizeAndPosition) {
        try {
            LogKit.info("开始文档矫正和角点检测");
            
            // 1. 验证图片文件
            RetKit validationResult = validateImageFile(imageFile);
            if (!validationResult.isTrue("success")) {
                return validationResult;
            }
            
            // 2. 获取原图尺寸
            BufferedImage image = ImageIO.read(imageFile.getFile());
            if (image == null) {
                return RetKit.fail("无法读取图片文件，请检查图片格式");
            }
            
            int originalWidth = image.getWidth();
            int originalHeight = image.getHeight();
            
            LogKit.info("原图尺寸: " + originalWidth + "x" + originalHeight);
            
            // 3. 调用TextIn API进行矫正和角点检测
            LogKit.info("开始调用TextIn切边矫正API");
            CropEnhanceResponse response = CropEnhanceApiClient.me
                .correctAndDetect(imageFile.getFile(), enhanceMode, cropImage, sizeAndPosition);
            
            if (response == null || response.getResult() == null) {
                return RetKit.fail("TextIn切边矫正API调用失败，未返回有效结果");
            }
            
            // 4. 构建返回结果
            Map<String, Object> result = buildResult(originalWidth, originalHeight, response);
            
            LogKit.info("文档矫正和角点检测完成");
            return RetKit.ok("data", result);
            
        } catch (Exception e) {
            LogKit.error("文档矫正失败: " + e.getMessage(), e);
            return RetKit.fail("文档矫正失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证上传的图片文件
     */
    private RetKit validateImageFile(UploadFile imageFile) {
        if (imageFile == null) {
            return RetKit.fail("请上传图片文件");
        }
        
        File file = imageFile.getFile();
        if (file == null || !file.exists()) {
            return RetKit.fail("图片文件不存在");
        }
        
        // 检查文件大小
        if (file.length() > MAX_FILE_SIZE) {
            return RetKit.fail("图片文件过大，请上传小于10MB的图片");
        }
        
        // 检查文件格式
        String fileName = imageFile.getFileName();
        if (fileName == null || fileName.trim().isEmpty()) {
            return RetKit.fail("图片文件名无效");
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        boolean isSupported = false;
        for (String format : SUPPORTED_FORMATS) {
            if (format.equals(extension)) {
                isSupported = true;
                break;
            }
        }
        
        if (!isSupported) {
            return RetKit.fail("不支持的图片格式，请上传jpg、png、bmp、webp格式的图片");
        }
        
        return RetKit.ok();
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        return "";
    }
    
    /**
     * 构建返回结果
     */
    private Map<String, Object> buildResult(int originalWidth, int originalHeight, 
                                          CropEnhanceResponse response) {
        Map<String, Object> result = new HashMap<>();
        
        // 原图信息
        Map<String, Object> originalImage = new HashMap<>();
        originalImage.put("width", originalWidth);
        originalImage.put("height", originalHeight);
        result.put("originalImage", originalImage);
        
        // 处理矫正后的图片信息
        CropEnhanceResponse.Result apiResult = response.getResult();
        if (apiResult.getImageList() != null && !apiResult.getImageList().isEmpty()) {
            CropEnhanceResponse.ImageInfo imageInfo = apiResult.getImageList().get(0);
            
            // 矫正后图片信息
            Map<String, Object> correctedImage = new HashMap<>();
            correctedImage.put("base64", imageInfo.getImage());
            correctedImage.put("width", imageInfo.getCroppedWidth());
            correctedImage.put("height", imageInfo.getCroppedHeight());
            result.put("correctedImage", correctedImage);
            
            // 角点坐标
            if (imageInfo.getPosition() != null && imageInfo.getPosition().length >= 8) {
                result.put("cropPoints", convertCropPoints(imageInfo.getPosition()));
            } else {
                result.put("cropPoints", new ArrayList<>());
            }
            
            // 角度信息
            result.put("angle", imageInfo.getAngle() != null ? imageInfo.getAngle() : 0);
        } else {
            // 如果没有返回图片信息，返回空数据
            result.put("correctedImage", null);
            result.put("cropPoints", new ArrayList<>());
            result.put("angle", 0);
        }
        
        // 处理时间
        result.put("processingTime", response.getDuration() != null ? response.getDuration() : 0);
        
        return result;
    }
    
    /**
     * 转换角点坐标格式
     * TextIn返回格式：[x1,y1,x2,y2,x3,y3,x4,y4]
     * 转换为APP格式：[{x:x1,y:y1},{x:x2,y:y2},{x:x3,y:y3},{x:x4,y:y4}]
     */
    private List<Map<String, Integer>> convertCropPoints(int[] position) {
        List<Map<String, Integer>> points = new ArrayList<>();
        
        for (int i = 0; i < position.length; i += 2) {
            if (i + 1 < position.length) {
                Map<String, Integer> point = new HashMap<>();
                point.put("x", position[i]);
                point.put("y", position[i + 1]);
                points.add(point);
            }
        }
        
        return points;
    }
}
