package com.sandu.xinye.api.v2.ocr;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;
import com.sandu.xinye.common.kit.RetKit;

/**
 * 文档切边矫正控制器
 * 提供文档矫正和角点检测接口
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
public class CropEnhanceController extends AppController {
    
    /**
     * 文档矫正和角点检测接口
     * POST /api/v2/crop-enhance
     * 
     * 请求参数:
     * - file: 图片文件 (multipart/form-data)
     * - enhanceMode: 增强模式(可选)
     *   * -1: 禁用增强(默认)
     *   * 1: 增亮
     *   * 2: 增强并锐化
     *   * 3: 黑白
     *   * 4: 灰度
     *   * 5: 去阴影增强
     *   * 6: 点阵图
     * - cropImage: 是否执行切边(可选)
     *   * 0: 不执行切边(默认)
     *   * 1: 执行切边
     * - sizeAndPosition: 自定义切边区域(可选)
     *   * 格式: "width,height,x1,y1,x2,y2,x3,y3,x4,y4"
     *   * width,height: 图像宽高，设置为0则使用默认值
     *   * (x1,y1): 左上角坐标, (x2,y2): 右上角坐标
     *   * (x3,y3): 右下角坐标, (x4,y4): 左下角坐标
     * 
     * 响应格式:
     * {
     *   "success": true,
     *   "message": "文档矫正成功",
     *   "data": {
     *     "originalImage": {
     *       "width": 2000,
     *       "height": 3000
     *     },
     *     "correctedImage": {
     *       "base64": "矫正后图片的base64编码",
     *       "width": 1800,
     *       "height": 2600
     *     },
     *     "cropPoints": [
     *       {"x": 100, "y": 150},   // 左上角
     *       {"x": 1800, "y": 120},  // 右上角
     *       {"x": 1850, "y": 2800}, // 右下角
     *       {"x": 80, "y": 2850}    // 左下角
     *     ],
     *     "angle": 0,
     *     "processingTime": 800
     *   }
     * }
     */
    @Before({AppUserInterceptor.class})
    public void index() {
        LogKit.info("文档切边矫正请求开始 - 用户ID: " + getUser().getUserId());
        
        try {
            // 获取上传的图片文件
            UploadFile imageFile = getFile("file");
            if (imageFile == null) {
                renderJson(RetKit.fail("请上传图片文件"));
                return;
            }
            
            // 获取可选参数
            Integer enhanceMode = getParaToInt("enhanceMode");
            Integer cropImage = getParaToInt("cropImage");
            String sizeAndPosition = getPara("sizeAndPosition");

            LogKit.info("接收到图片文件: " + imageFile.getFileName() +
                       ", 大小: " + imageFile.getFile().length() + " bytes" +
                       ", 增强模式: " + enhanceMode +
                       ", 是否切边: " + cropImage +
                       ", 自定义区域: " + sizeAndPosition);

            // 调用服务层处理
            RetKit result = CropEnhanceService.me.correctDocumentAndDetectCorners(imageFile, enhanceMode, cropImage, sizeAndPosition);
            
            if (result.isTrue("success")) {
                LogKit.info("文档切边矫正成功");
                result.setMsg("文档矫正成功");
            } else {
                LogKit.warn("文档切边矫正失败: " + result.getStr("msg"));
            }
            
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("文档切边矫正请求处理异常: " + e.getMessage(), e);
            renderJson(RetKit.fail("文档切边矫正处理异常: " + e.getMessage()));
        } finally {
            LogKit.info("文档切边矫正请求结束");
        }
    }
    
    /**
     * 健康检查接口
     * GET /api/v2/crop-enhance/health
     */
    public void health() {
        LogKit.info("文档切边矫正服务健康检查");
        
        try {
            // 检查基本配置
            String apiUrl = com.jfinal.kit.PropKit.get("textin.crop.api.url", "");
            String apiKey = com.jfinal.kit.PropKit.get("textin.api.key", "");
            String appId = com.jfinal.kit.PropKit.get("textin.app.id", "");
            
            boolean configValid = !apiUrl.isEmpty() && !apiKey.isEmpty() && !appId.isEmpty();
            
            RetKit result = RetKit.ok();
            result.setMsg(configValid ? "文档切边矫正服务运行正常" : "配置缺失");

            java.util.Map<String, Object> data = new java.util.HashMap<>();
            data.put("service", "crop-enhance");
            data.put("status", configValid ? "healthy" : "config_missing");
            data.put("timestamp", System.currentTimeMillis());
            java.util.Map<String, String> config = new java.util.HashMap<>();
            config.put("apiUrl", !apiUrl.isEmpty() ? "configured" : "missing");
            config.put("apiKey", !apiKey.isEmpty() ? "configured" : "missing");
            config.put("appId", !appId.isEmpty() ? "configured" : "missing");
            data.put("config", config);
            result.set("data", data);
            
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("文档切边矫正服务健康检查异常: " + e.getMessage(), e);
            renderJson(RetKit.fail("健康检查失败: " + e.getMessage()));
        }
    }
    

}
